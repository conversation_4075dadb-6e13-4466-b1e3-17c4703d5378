package com.workly.app.services

import android.Manifest
import android.app.AlarmManager
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import com.workly.app.R
import com.workly.app.WorklyApplication
import com.workly.app.data.model.Note
import com.workly.app.data.model.Shift
import com.workly.app.receivers.AlarmReceiver
import com.workly.app.ui.MainActivity
import dagger.hilt.android.qualifiers.ApplicationContext
import java.time.LocalDateTime
import java.time.ZoneId
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class NotificationServiceImpl @Inject constructor(
    @ApplicationContext private val context: Context
) : NotificationService {

    private val notificationManager = NotificationManagerCompat.from(context)
    private val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager

    override suspend fun scheduleShiftReminder(shift: Shift, reminderTime: Long) {
        if (!hasNotificationPermission()) return

        // Tạo ID duy nhất cho nhắc nhở (để hỗ trợ Just-In-Time logic)
        val reminderId = generateReminderId(shift.id, reminderTime)

        val intent = Intent(context, AlarmReceiver::class.java).apply {
            action = "SHIFT_REMINDER"
            putExtra("reminder_id", reminderId)
            putExtra("shift_id", shift.id)
            putExtra("shift_name", shift.name)
            putExtra("title", "Nhắc nhở ca làm việc")
            putExtra("message", "Ca ${shift.name} sắp bắt đầu lúc ${shift.startTime}")
            putExtra("reminder_time", reminderTime)
        }

        val pendingIntent = PendingIntent.getBroadcast(
            context,
            reminderId.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        try {
            alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP,
                reminderTime,
                pendingIntent
            )
        } catch (e: SecurityException) {
            // Handle case where exact alarms are not allowed
            alarmManager.set(AlarmManager.RTC_WAKEUP, reminderTime, pendingIntent)
        }
    }

    override suspend fun scheduleNoteReminder(note: Note) {
        if (!hasNotificationPermission() || note.reminderDateTime == null) return

        val reminderTime = note.reminderDateTime.atZone(ZoneId.systemDefault()).toEpochMilli()

        val intent = Intent(context, AlarmReceiver::class.java).apply {
            action = "NOTE_REMINDER"
            putExtra("note_id", note.id)
            putExtra("note_title", note.title)
            putExtra("title", "Nhắc nhở ghi chú")
            putExtra("message", note.title)
        }

        val pendingIntent = PendingIntent.getBroadcast(
            context,
            note.id.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        try {
            alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP,
                reminderTime,
                pendingIntent
            )
        } catch (e: SecurityException) {
            alarmManager.set(AlarmManager.RTC_WAKEUP, reminderTime, pendingIntent)
        }
    }

    override suspend fun cancelShiftReminder(shiftId: String) {
        // Hủy nhắc nhở theo pattern ID (hỗ trợ Just-In-Time logic)
        val reminderPatterns = listOf(
            "departure-",
            "checkin-",
            "checkout-"
        )

        reminderPatterns.forEach { pattern ->
            val reminderId = "$pattern$shiftId"
            cancelReminderById(reminderId)
        }

        // Hủy theo cách cũ để backward compatibility
        val intent = Intent(context, AlarmReceiver::class.java)
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            shiftId.hashCode(),
            intent,
            PendingIntent.FLAG_NO_CREATE or PendingIntent.FLAG_IMMUTABLE
        )

        pendingIntent?.let {
            alarmManager.cancel(it)
            it.cancel()
        }
    }

    private fun cancelReminderById(reminderId: String) {
        val intent = Intent(context, AlarmReceiver::class.java)
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            reminderId.hashCode(),
            intent,
            PendingIntent.FLAG_NO_CREATE or PendingIntent.FLAG_IMMUTABLE
        )

        pendingIntent?.let {
            alarmManager.cancel(it)
            it.cancel()
        }
    }

    private fun generateReminderId(shiftId: String, reminderTime: Long): String {
        val date = java.time.Instant.ofEpochMilli(reminderTime)
            .atZone(java.time.ZoneId.systemDefault())
            .toLocalDate()
        return "$shiftId-$date"
    }

    override suspend fun cancelNoteReminder(noteId: String) {
        val intent = Intent(context, AlarmReceiver::class.java)
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            noteId.hashCode(),
            intent,
            PendingIntent.FLAG_NO_CREATE or PendingIntent.FLAG_IMMUTABLE
        )

        pendingIntent?.let {
            alarmManager.cancel(it)
            it.cancel()
        }
    }

    override suspend fun cancelAllReminders() {
        // This would require keeping track of all scheduled reminders
        // For now, we'll implement a basic version
    }

    override suspend fun showInstantNotification(title: String, message: String, channelId: String) {
        if (!hasNotificationPermission()) return

        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }

        val pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, channelId)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(message)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build()

        notificationManager.notify(System.currentTimeMillis().toInt(), notification)
    }

    override suspend fun hasNotificationPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            notificationManager.areNotificationsEnabled()
        }
    }

    override suspend fun requestNotificationPermission(): Boolean {
        return hasNotificationPermission()
    }

    override suspend fun updateNotificationSettings(soundEnabled: Boolean, vibrationEnabled: Boolean) {
        // Update notification channel settings if needed
        // This would typically involve recreating notification channels with new settings
    }
}
