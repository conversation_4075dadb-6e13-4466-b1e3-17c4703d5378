package com.workly.app.ui.dialogs

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.workly.app.data.model.*
import com.workly.app.data.repository.AttendanceRepository
import com.workly.app.data.repository.DailyWorkStatusRepository
import com.workly.app.data.repository.SettingsRepository
import com.workly.app.data.repository.ShiftRepository
import com.workly.app.data.repository.WorkCalculationRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.temporal.ChronoUnit
import javax.inject.Inject

@HiltViewModel
class UpdateStatusViewModel @Inject constructor(
    private val dailyWorkStatusRepository: DailyWorkStatusRepository,
    private val attendanceRepository: AttendanceRepository,
    private val workCalculationRepository: WorkCalculationRepository,
    private val shiftRepository: ShiftRepository,
    private val settingsRepository: SettingsRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(UpdateStatusUiState())
    val uiState: StateFlow<UpdateStatusUiState> = _uiState.asStateFlow()

    private val _uiEffect = MutableSharedFlow<UpdateStatusUiEffect>()
    val uiEffect: SharedFlow<UpdateStatusUiEffect> = _uiEffect.asSharedFlow()

    private var selectedDate: LocalDate? = null
    private var currentDailyStatus: DailyWorkStatus? = null
    private var currentShift: Shift? = null

    fun loadStatusForDate(date: LocalDate) {
        selectedDate = date
        
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                // Load current status
                currentDailyStatus = dailyWorkStatusRepository.getDailyWorkStatus(date)
                
                // Load current shift
                val settings = settingsRepository.getUserSettings().first()
                currentShift = settings.activeShiftId?.let { 
                    shiftRepository.getShiftById(it) 
                }
                
                // Update UI state
                updateUIState()
                
            } catch (e: Exception) {
                _uiEffect.emit(UpdateStatusUiEffect.ShowError("Không thể tải dữ liệu: ${e.message}"))
            } finally {
                _uiState.value = _uiState.value.copy(isLoading = false)
            }
        }
    }

    fun setUpdateType(updateType: UpdateType) {
        _uiState.value = _uiState.value.copy(
            updateType = updateType,
            validationResult = null
        )
        validateCurrentState()
    }

    fun selectLeaveStatus(leaveStatus: LeaveStatus) {
        _uiState.value = _uiState.value.copy(
            selectedLeaveStatus = leaveStatus,
            availableLeaveTypes = _uiState.value.availableLeaveTypes.map { item ->
                item.copy(isSelected = item.leaveStatus == leaveStatus)
            }
        )
        validateCurrentState()
    }

    fun setCheckInTime(time: LocalTime) {
        _uiState.value = _uiState.value.copy(checkInTime = time)
        validateAttendanceTimes()
    }

    fun setCheckOutTime(time: LocalTime) {
        _uiState.value = _uiState.value.copy(checkOutTime = time)
        validateAttendanceTimes()
    }

    fun saveChanges() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val currentState = _uiState.value
                val date = selectedDate ?: return@launch
                
                when (currentState.updateType) {
                    UpdateType.LEAVE_STATUS -> {
                        saveLeaveStatus(date, currentState.selectedLeaveStatus)
                    }
                    UpdateType.EDIT_ATTENDANCE -> {
                        saveAttendanceTimes(date, currentState.checkInTime, currentState.checkOutTime)
                    }
                }
                
                _uiEffect.emit(UpdateStatusUiEffect.ShowSuccess("Cập nhật thành công"))
                
            } catch (e: Exception) {
                _uiEffect.emit(UpdateStatusUiEffect.ShowError("Lỗi khi lưu: ${e.message}"))
            } finally {
                _uiState.value = _uiState.value.copy(isLoading = false)
            }
        }
    }

    private suspend fun saveLeaveStatus(date: LocalDate, leaveStatus: LeaveStatus?) {
        if (leaveStatus == null) return
        
        // Create or update daily work status with leave status
        val updatedStatus = currentDailyStatus?.copy(
            leaveStatus = leaveStatus,
            complianceStatus = ComplianceStatus.MANUAL_OVERRIDE,
            isManualOverride = true,
            // Reset work hours for leave days
            standardHoursScheduled = 0.0,
            otHoursScheduled = 0.0,
            sundayHoursScheduled = 0.0,
            nightHoursScheduled = 0.0,
            holidayHoursScheduled = 0.0,
            totalHoursScheduled = 0.0,
            lastCalculatedAt = LocalDateTime.now()
        ) ?: DailyWorkStatus(
            date = date,
            complianceStatus = ComplianceStatus.MANUAL_OVERRIDE,
            leaveStatus = leaveStatus,
            isManualOverride = true,
            lastCalculatedAt = LocalDateTime.now()
        )
        
        dailyWorkStatusRepository.insertOrUpdateDailyWorkStatus(updatedStatus)
    }

    private suspend fun saveAttendanceTimes(
        date: LocalDate,
        checkInTime: LocalTime?,
        checkOutTime: LocalTime?
    ) {
        if (checkInTime == null || checkOutTime == null) return
        
        val checkInDateTime = date.atTime(checkInTime)
        val checkOutDateTime = date.atTime(checkOutTime)
        
        // Create or update attendance logs
        val existingLogs = attendanceRepository.getLogsForDate(date)
        
        // Remove existing check-in/check-out logs
        existingLogs.filter { 
            it.type == AttendanceType.CHECK_IN || it.type == AttendanceType.CHECK_OUT 
        }.forEach { log ->
            attendanceRepository.deleteAttendanceLog(log.id)
        }
        
        // Create new logs
        val checkInLog = AttendanceLog(
            id = "manual_checkin_${date}_${System.currentTimeMillis()}",
            type = AttendanceType.CHECK_IN,
            time = checkInDateTime,
            workDate = date,
            isManualEntry = true,
            note = "Chỉnh sửa thủ công"
        )
        
        val checkOutLog = AttendanceLog(
            id = "manual_checkout_${date}_${System.currentTimeMillis()}",
            type = AttendanceType.CHECK_OUT,
            time = checkOutDateTime,
            workDate = date,
            isManualEntry = true,
            note = "Chỉnh sửa thủ công"
        )
        
        attendanceRepository.insertAttendanceLog(checkInLog)
        attendanceRepository.insertAttendanceLog(checkOutLog)
        
        // Trigger recalculation
        currentShift?.let { shift ->
            workCalculationRepository.calculateAndUpdateDailyWork(
                date = date,
                shiftId = shift.id,
                forceRecalculate = true
            )
        }
    }

    private fun updateUIState() {
        val currentStatusText = getCurrentStatusText()
        val leaveTypes = getAvailableLeaveTypes()
        val (checkInTime, checkOutTime) = getCurrentAttendanceTimes()
        
        _uiState.value = _uiState.value.copy(
            currentStatusText = currentStatusText,
            availableLeaveTypes = leaveTypes,
            checkInTime = checkInTime,
            checkOutTime = checkOutTime
        )
        
        validateCurrentState()
    }

    private fun getCurrentStatusText(): String {
        return currentDailyStatus?.let { status ->
            when {
                status.leaveStatus != null -> status.leaveStatus!!.getDisplayText()
                status.complianceStatus != ComplianceStatus.NOT_STARTED -> 
                    status.complianceStatus.getDisplayText()
                else -> "Chưa có dữ liệu"
            }
        } ?: "Chưa có dữ liệu"
    }

    private fun getAvailableLeaveTypes(): List<LeaveTypeItem> {
        return LeaveStatus.values().map { leaveStatus ->
            LeaveTypeItem(
                leaveStatus = leaveStatus,
                title = leaveStatus.getDisplayText().removePrefix("🏖️ ").removePrefix("🤒 ")
                    .removePrefix("👤 ").removePrefix("🤱 ").removePrefix("👨‍👶 ")
                    .removePrefix("🕊️ ").removePrefix("🔄 ").removePrefix("🎉 ")
                    .removePrefix("💸 ").removePrefix("✈️ ").removePrefix("📚 ")
                    .removePrefix("📝 "),
                description = getLeaveDescription(leaveStatus),
                icon = getLeaveIcon(leaveStatus),
                isSelected = currentDailyStatus?.leaveStatus == leaveStatus
            )
        }
    }

    private fun getCurrentAttendanceTimes(): Pair<LocalTime?, LocalTime?> {
        val date = selectedDate ?: return Pair(null, null)
        
        // Get from current daily status or default from shift
        val checkInTime = currentDailyStatus?.actualCheckInTime?.toLocalTime()
            ?: currentShift?.let { LocalTime.parse(it.startTime) }
        
        val checkOutTime = currentDailyStatus?.actualCheckOutTime?.toLocalTime()
            ?: currentShift?.let { LocalTime.parse(it.endTime) }
        
        return Pair(checkInTime, checkOutTime)
    }

    private fun validateCurrentState() {
        val currentState = _uiState.value
        
        val canSave = when (currentState.updateType) {
            UpdateType.LEAVE_STATUS -> currentState.selectedLeaveStatus != null
            UpdateType.EDIT_ATTENDANCE -> {
                val validation = validateAttendanceTimes()
                validation.isValid
            }
        }
        
        _uiState.value = _uiState.value.copy(canSave = canSave)
    }

    private fun validateAttendanceTimes(): ValidationResult {
        val currentState = _uiState.value
        val checkInTime = currentState.checkInTime
        val checkOutTime = currentState.checkOutTime
        val date = selectedDate
        
        if (checkInTime == null || checkOutTime == null || date == null) {
            return ValidationResult(false, "Vui lòng chọn đầy đủ thời gian")
        }
        
        // Check if check-out is after check-in
        if (checkOutTime.isBefore(checkInTime)) {
            val validation = ValidationResult(false, "Giờ ra về phải sau giờ vào làm")
            _uiState.value = _uiState.value.copy(validationResult = validation)
            return validation
        }
        
        // Check if times are reasonable (not too far from shift schedule)
        val warnings = mutableListOf<String>()
        
        currentShift?.let { shift ->
            val shiftStart = LocalTime.parse(shift.startTime)
            val shiftEnd = LocalTime.parse(shift.endTime)
            
            val checkInDiff = ChronoUnit.MINUTES.between(shiftStart, checkInTime)
            val checkOutDiff = ChronoUnit.MINUTES.between(shiftEnd, checkOutTime)
            
            if (kotlin.math.abs(checkInDiff) > 60) {
                warnings.add("Giờ vào làm khác xa so với lịch trình ca (${kotlin.math.abs(checkInDiff)} phút)")
            }
            
            if (kotlin.math.abs(checkOutDiff) > 60) {
                warnings.add("Giờ ra về khác xa so với lịch trình ca (${kotlin.math.abs(checkOutDiff)} phút)")
            }
        }
        
        // Check if date is in the future
        if (date.isAfter(LocalDate.now())) {
            warnings.add("Đây là ngày trong tương lai")
        }
        
        val validation = ValidationResult(
            isValid = true,
            warningMessage = warnings.firstOrNull()
        )
        
        _uiState.value = _uiState.value.copy(validationResult = validation)
        return validation
    }

    private fun getLeaveDescription(leaveStatus: LeaveStatus): String? {
        return when (leaveStatus) {
            LeaveStatus.ANNUAL_LEAVE -> "Nghỉ phép có lương theo quy định"
            LeaveStatus.SICK_LEAVE -> "Nghỉ ốm có xác nhận y tế"
            LeaveStatus.PERSONAL_LEAVE -> "Nghỉ việc riêng cá nhân"
            LeaveStatus.BUSINESS_TRIP -> "Đi công tác theo yêu cầu công việc"
            LeaveStatus.PUBLIC_HOLIDAY -> "Nghỉ lễ theo quy định nhà nước"
            else -> null
        }
    }

    private fun getLeaveIcon(leaveStatus: LeaveStatus): String {
        return when (leaveStatus) {
            LeaveStatus.ANNUAL_LEAVE -> "🏖️"
            LeaveStatus.SICK_LEAVE -> "🤒"
            LeaveStatus.PERSONAL_LEAVE -> "👤"
            LeaveStatus.MATERNITY_LEAVE -> "🤱"
            LeaveStatus.PATERNITY_LEAVE -> "👨‍👶"
            LeaveStatus.BEREAVEMENT_LEAVE -> "🕊️"
            LeaveStatus.COMPENSATORY_LEAVE -> "🔄"
            LeaveStatus.PUBLIC_HOLIDAY -> "🎉"
            LeaveStatus.UNPAID_LEAVE -> "💸"
            LeaveStatus.BUSINESS_TRIP -> "✈️"
            LeaveStatus.TRAINING -> "📚"
            LeaveStatus.OTHER -> "📝"
        }
    }
}
