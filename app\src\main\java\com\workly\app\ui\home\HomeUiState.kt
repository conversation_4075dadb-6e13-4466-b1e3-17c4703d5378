package com.workly.app.ui.home

import com.workly.app.data.model.*
import com.workly.app.services.AttendanceButtonState
import com.workly.app.services.AttendanceStatusInfo
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * UI State cho HomeScreen theo thiết kế mới
 */
data class HomeUiState(
    // Header information
    val currentTime: LocalDateTime = LocalDateTime.now(),
    val activeShift: Shift? = null,
    val isLoading: Boolean = false,
    
    // Weather widget
    val weatherData: WeatherData? = null,
    val weatherWarning: WeatherWarning? = null,
    val isWeatherLoading: Boolean = false,
    
    // Multi-function button
    val buttonState: AttendanceButtonState = AttendanceButtonState.HIDDEN,
    val buttonText: String = "",
    val buttonIcon: String = "",
    val buttonColor: Int = 0,
    val isButtonVisible: Boolean = false,
    val buttonProgress: Int = 0, // 0-100
    val buttonStatusText: String? = null,
    
    // Weekly status grid
    val weeklyStatus: List<WeeklyStatusItem> = emptyList(),
    val currentWeekRange: String = "",
    
    // Notes section
    val displayedNotes: List<Note> = emptyList(),
    val notesDisplayCount: Int = 3,
    val hasMoreNotes: Boolean = false,
    
    // Quick stats
    val thisWeekHours: Double = 0.0,
    val complianceScore: Double = 0.0,
    val isStatsVisible: Boolean = true,
    
    // Status info
    val statusInfo: AttendanceStatusInfo? = null,
    
    // Error states
    val errorMessage: String? = null,
    val hasError: Boolean = false
)

/**
 * Item cho lưới trạng thái tuần
 */
data class WeeklyStatusItem(
    val date: LocalDate,
    val dayOfWeek: String, // "T2", "T3", etc.
    val dayNumber: String, // "02", "03", etc.
    val status: ComplianceStatus,
    val statusIcon: String,
    val statusColor: Int,
    val isToday: Boolean = false,
    val isClickable: Boolean = true
)

/**
 * Cảnh báo thời tiết
 */
data class WeatherWarning(
    val type: WeatherWarningType,
    val message: String,
    val severity: WeatherWarningSeverity,
    val icon: String
)

enum class WeatherWarningSeverity {
    LOW, MEDIUM, HIGH, CRITICAL
}

/**
 * Events từ UI
 */
sealed class HomeUiEvent {
    object RefreshWeather : HomeUiEvent()
    object ButtonPressed : HomeUiEvent()
    object SettingsClicked : HomeUiEvent()
    object ViewAllNotes : HomeUiEvent()
    object AddNote : HomeUiEvent()
    object ViewAllStatus : HomeUiEvent()
    data class StatusItemClicked(val date: LocalDate) : HomeUiEvent()
    data class NoteClicked(val noteId: String) : HomeUiEvent()
    data class EditNote(val noteId: String) : HomeUiEvent()
    data class DeleteNote(val noteId: String) : HomeUiEvent()
    object DismissError : HomeUiEvent()
}

/**
 * Side effects
 */
sealed class HomeUiEffect {
    object NavigateToSettings : HomeUiEffect()
    object NavigateToNotes : HomeUiEffect()
    object NavigateToStatistics : HomeUiEffect()
    data class NavigateToNoteDetail(val noteId: String) : HomeUiEffect()
    data class ShowStatusModal(val date: LocalDate) : HomeUiEffect()
    data class ShowMessage(val message: String) : HomeUiEffect()
    data class ShowError(val error: String) : HomeUiEffect()
    object TriggerHapticFeedback : HomeUiEffect()
    object PlaySoundFeedback : HomeUiEffect()
}
