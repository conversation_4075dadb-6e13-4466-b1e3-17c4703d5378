<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Header Section -->
        <LinearLayout
            android:id="@+id/layout_header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/text_view_current_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                    android:textColor="?attr/colorOnSurface"
                    tools:text="Thứ Hai, 02/07 08:30" />

                <TextView
                    android:id="@+id/text_view_shift_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    tools:text="Ca hành chính" />

            </LinearLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/button_settings"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                app:icon="@drawable/ic_settings"
                app:iconSize="24dp" />

        </LinearLayout>

        <!-- Weather Widget -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_weather"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_header">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/text_view_weather_temp"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textAppearance="@style/TextAppearance.Material3.HeadlineMedium"
                            android:textColor="?attr/colorOnSurface"
                            tools:text="28°C" />

                        <ImageView
                            android:id="@+id/image_view_weather_icon"
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:layout_marginStart="8dp"
                            android:contentDescription="Weather icon"
                            tools:src="@drawable/ic_sunny" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/text_view_weather_description"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        tools:text="Nắng ít mây" />

                </LinearLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_weather_refresh"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    app:icon="@drawable/ic_refresh"
                    app:iconSize="20dp" />

            </LinearLayout>

            <!-- Weather Warning Area (Dynamic) -->
            <LinearLayout
                android:id="@+id/layout_weather_warning"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="12dp"
                android:background="?attr/colorErrorContainer"
                android:visibility="gone"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_warning"
                    android:layout_marginEnd="8dp"
                    app:tint="?attr/colorOnErrorContainer" />

                <TextView
                    android:id="@+id/text_view_weather_warning"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                    android:textColor="?attr/colorOnErrorContainer"
                    tools:text="Cảnh báo: Có mưa to, hãy mang theo ô" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Multi-Function Dynamic Button -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_multi_button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/card_weather">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_multi_function"
                    android:layout_width="match_parent"
                    android:layout_height="72dp"
                    android:text="Đi làm"
                    android:textSize="20sp"
                    app:cornerRadius="36dp"
                    app:icon="@drawable/ic_work"
                    app:iconSize="28dp"
                    app:iconGravity="textStart" />

                <!-- Progress indicator for button state -->
                <ProgressBar
                    android:id="@+id/progress_bar_loading"
                    style="?android:attr/progressBarStyleHorizontal"
                    android:layout_width="match_parent"
                    android:layout_height="4dp"
                    android:layout_marginTop="8dp"
                    android:visibility="gone"
                    android:progress="0"
                    android:max="100" />

                <!-- Status text below button -->
                <TextView
                    android:id="@+id/text_view_button_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="8dp"
                    android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                    android:textColor="?attr/colorOnSurfaceVariant"
                    android:visibility="gone"
                    tools:text="Trong cửa sổ hoạt động" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Weekly Status Grid -->
        <LinearLayout
            android:id="@+id/layout_weekly_grid_header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/card_multi_button">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Trạng thái tuần"
                android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                android:textColor="?attr/colorOnSurface" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/button_view_all_status"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Xem tất cả"
                android:textSize="12sp" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_weekly_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:nestedScrollingEnabled="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_weekly_grid_header"
            tools:itemCount="7"
            tools:listitem="@layout/item_weekly_status" />

        <!-- Notes Section -->
        <LinearLayout
            android:id="@+id/layout_notes_header"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/recycler_view_weekly_status">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Ghi chú"
                android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                android:textColor="?attr/colorOnSurface" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/button_view_all_notes"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="40dp"
                android:layout_height="40dp"
                app:icon="@drawable/ic_menu"
                app:iconSize="20dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/button_add_note"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="40dp"
                android:layout_height="40dp"
                app:icon="@drawable/ic_add"
                app:iconSize="20dp" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_notes"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:nestedScrollingEnabled="false"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_notes_header"
            tools:itemCount="3"
            tools:listitem="@layout/item_note_home" />

        <!-- Quick Stats Section (Optional) -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_quick_stats"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/recycler_view_notes">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/text_view_this_week_hours"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                        android:textColor="?attr/colorPrimary"
                        tools:text="32.5h" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Tuần này"
                        android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="?attr/colorOutlineVariant" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:gravity="center">

                    <TextView
                        android:id="@+id/text_view_compliance_score"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textAppearance="@style/TextAppearance.Material3.HeadlineSmall"
                        android:textColor="?attr/colorSecondary"
                        tools:text="95%" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Tuân thủ"
                        android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                        android:textColor="?attr/colorOnSurfaceVariant" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>
