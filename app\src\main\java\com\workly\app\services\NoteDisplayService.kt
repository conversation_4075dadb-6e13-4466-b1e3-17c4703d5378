package com.workly.app.services

import com.workly.app.data.model.*
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * Service xử lý logic hiển thị ghi chú nâng cao
 * Implement logic "Slot Đảm bảo + Ưu tiên"
 */
interface NoteDisplayService {
    
    /**
     * <PERSON><PERSON><PERSON> danh sách ghi chú để hiển thị trên HomeScreen
     * Áp dụng logic "Slot Đảm bảo + Ưu tiên"
     */
    suspend fun getNotesForHomeScreen(
        maxDisplayCount: Int,
        currentShiftId: String? = null,
        currentDate: LocalDate = LocalDate.now()
    ): List<Note>
    
    /**
     * Sắp xếp ghi chú theo độ ưu tiên hiển thị
     */
    suspend fun sortNotesByDisplayPriority(
        notes: List<Note>,
        sortOrder: NotesSortOrder = NotesSortOrder.PRIORITY_FIRST
    ): List<Note>
    
    /**
     * <PERSON><PERSON><PERSON> ghi chú theo ngữ cảnh (ca làm việc, ng<PERSON><PERSON>, thời gian)
     */
    suspend fun filterNotesByContext(
        notes: List<Note>,
        context: NoteDisplayContext
    ): List<Note>
    
    /**
     * Kiểm tra xung đột thời gian giữa các ghi chú
     */
    suspend fun checkTimeConflicts(
        notes: List<Note>,
        timeWindow: NotesTimeWindow
    ): List<NoteConflict>
    
    /**
     * Lấy ghi chú cần nhắc nhở tiếp theo
     */
    suspend fun getNextReminderNotes(
        fromTime: LocalDateTime = LocalDateTime.now(),
        lookAheadHours: Int = 24
    ): List<Note>
    
    /**
     * Cập nhật thống kê xem ghi chú
     */
    suspend fun updateNoteViewStats(noteId: String)
    
    /**
     * Tự động ẩn ghi chú đã hoàn thành (nếu được cấu hình)
     */
    suspend fun autoHideCompletedNotes()
    
    /**
     * Đề xuất ghi chú dựa trên lịch sử và ngữ cảnh
     */
    suspend fun suggestRelevantNotes(
        context: NoteDisplayContext,
        limit: Int = 5
    ): List<Note>
}

/**
 * Context cho việc hiển thị ghi chú
 */
data class NoteDisplayContext(
    val currentTime: LocalDateTime,
    val currentDate: LocalDate,
    val activeShift: Shift?,
    val userLocation: String? = null,
    val timeWindow: NotesTimeWindow,
    val userPreferences: NoteDisplayPreferences
)

/**
 * Cấu hình hiển thị ghi chú
 */
data class NoteDisplayPreferences(
    val maxDisplayCount: Int = 3,
    val sortOrder: NotesSortOrder = NotesSortOrder.PRIORITY_FIRST,
    val timeWindow: NotesTimeWindow = NotesTimeWindow.ALWAYS,
    val showConflictWarning: Boolean = true,
    val autoHideCompleted: Boolean = false,
    val enableSmartSuggestions: Boolean = true,
    val guaranteedSlotCount: Int = 1 // Số slot đảm bảo tối thiểu
)

/**
 * Xung đột thời gian giữa ghi chú
 */
data class NoteConflict(
    val note1: Note,
    val note2: Note,
    val conflictType: ConflictType,
    val overlapMinutes: Int,
    val severity: ConflictSeverity,
    val suggestion: String
)

enum class ConflictType {
    TIME_OVERLAP,       // Chồng lấp thời gian
    SAME_LOCATION,      // Cùng địa điểm
    RESOURCE_CONFLICT,  // Xung đột tài nguyên
    PRIORITY_CONFLICT   // Xung đột ưu tiên
}

enum class ConflictSeverity {
    LOW, MEDIUM, HIGH, CRITICAL
}

/**
 * Kết quả phân tích hiển thị ghi chú
 */
data class NoteDisplayAnalysis(
    val totalNotes: Int,
    val displayedNotes: Int,
    val guaranteedSlotNotes: Int,
    val priorityNotes: Int,
    val hiddenNotes: Int,
    val conflicts: List<NoteConflict>,
    val suggestions: List<String>
)

/**
 * Chiến lược hiển thị ghi chú
 */
interface NoteDisplayStrategy {
    suspend fun selectNotesForDisplay(
        allNotes: List<Note>,
        context: NoteDisplayContext
    ): List<Note>
    
    suspend fun calculateDisplayPriority(
        note: Note,
        context: NoteDisplayContext
    ): Double
}

/**
 * Chiến lược mặc định: Slot Đảm bảo + Ưu tiên
 */
class GuaranteedSlotPriorityStrategy : NoteDisplayStrategy {
    
    override suspend fun selectNotesForDisplay(
        allNotes: List<Note>,
        context: NoteDisplayContext
    ): List<Note> {
        val activeNotes = allNotes.filter { it.isActive() }
        val contextFilteredNotes = filterByContext(activeNotes, context)
        
        // Phân loại ghi chú
        val guaranteedSlotNotes = contextFilteredNotes.filter { it.isGuaranteedSlot }
        val priorityNotes = contextFilteredNotes.filter { it.isPriority && !it.isGuaranteedSlot }
        val pinnedNotes = contextFilteredNotes.filter { it.isPinned && !it.isPriority && !it.isGuaranteedSlot }
        val normalNotes = contextFilteredNotes.filter { 
            !it.isGuaranteedSlot && !it.isPriority && !it.isPinned 
        }
        
        val result = mutableListOf<Note>()
        val maxCount = context.userPreferences.maxDisplayCount
        
        // 1. Luôn thêm ghi chú slot đảm bảo
        result.addAll(guaranteedSlotNotes.sortedByDescending { it.createdAt })
        
        // 2. Thêm ghi chú ưu tiên (trong giới hạn slot còn lại)
        val remainingSlots = maxCount - result.size
        if (remainingSlots > 0) {
            val sortedPriorityNotes = priorityNotes.sortedWith(
                compareByDescending<Note> { calculateDisplayPriority(it, context) }
                    .thenByDescending { it.createdAt }
            )
            result.addAll(sortedPriorityNotes.take(remainingSlots))
        }
        
        // 3. Thêm ghi chú ghim (nếu còn slot)
        val remainingSlots2 = maxCount - result.size
        if (remainingSlots2 > 0) {
            result.addAll(pinnedNotes.sortedByDescending { it.createdAt }.take(remainingSlots2))
        }
        
        // 4. Thêm ghi chú bình thường (nếu còn slot)
        val remainingSlots3 = maxCount - result.size
        if (remainingSlots3 > 0) {
            val sortedNormalNotes = normalNotes.sortedWith(
                compareByDescending<Note> { calculateDisplayPriority(it, context) }
                    .thenByDescending { it.createdAt }
            )
            result.addAll(sortedNormalNotes.take(remainingSlots3))
        }
        
        return result
    }
    
    override suspend fun calculateDisplayPriority(
        note: Note,
        context: NoteDisplayContext
    ): Double {
        var priority = 0.0
        
        // Base priority từ loại ghi chú
        priority += when (note.getDisplayPriority()) {
            DisplayPriority.GUARANTEED_SLOT -> 1000.0
            DisplayPriority.HIGH_PRIORITY -> 100.0
            DisplayPriority.PINNED -> 50.0
            DisplayPriority.NORMAL -> 10.0
            DisplayPriority.LOW -> 1.0
        }
        
        // Bonus cho ghi chú có nhắc nhở sắp tới
        note.getNextReminderTime()?.let { reminderTime ->
            val hoursUntilReminder = java.time.Duration.between(
                context.currentTime, 
                reminderTime
            ).toHours()
            
            if (hoursUntilReminder in 0..24) {
                priority += (24 - hoursUntilReminder) * 2.0
            }
        }
        
        // Bonus cho ghi chú liên quan đến ca hiện tại
        if (context.activeShift != null && note.isRelevantForShift(context.activeShift.id)) {
            priority += 20.0
        }
        
        // Bonus cho ghi chú liên quan đến ngày hiện tại
        if (note.isRelevantForDate(context.currentDate)) {
            priority += 15.0
        }
        
        // Penalty cho ghi chú cũ (không có nhắc nhở)
        if (note.reminderDateTime == null) {
            val daysOld = java.time.Duration.between(note.createdAt, context.currentTime).toDays()
            priority -= daysOld * 0.5
        }
        
        // Bonus cho ghi chú được xem nhiều
        priority += note.viewCount * 0.1
        
        return priority
    }
    
    private fun filterByContext(notes: List<Note>, context: NoteDisplayContext): List<Note> {
        return notes.filter { note ->
            // Lọc theo thời gian nếu cần
            when (context.timeWindow) {
                NotesTimeWindow.ALWAYS -> true
                NotesTimeWindow.TODAY -> note.isRelevantForDate(context.currentDate)
                NotesTimeWindow.THIS_WEEK -> {
                    val weekStart = context.currentDate.minusDays(context.currentDate.dayOfWeek.value - 1L)
                    val weekEnd = weekStart.plusDays(6)
                    note.associatedDates.any { dateStr ->
                        val date = LocalDate.parse(dateStr)
                        !date.isBefore(weekStart) && !date.isAfter(weekEnd)
                    } || note.associatedDates.isEmpty()
                }
                NotesTimeWindow.NEXT_24_HOURS -> {
                    note.getNextReminderTime()?.let { reminderTime ->
                        reminderTime.isBefore(context.currentTime.plusHours(24))
                    } ?: false
                }
            }
        }
    }
}
