package com.workly.app.ui.dialogs

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.workly.app.R
import com.workly.app.data.model.LeaveStatus
import com.workly.app.databinding.ItemLeaveTypeBinding

/**
 * Adapter cho danh sách loại nghỉ trong modal cập nhật trạng thái
 */
class LeaveTypesAdapter(
    private val onLeaveTypeSelected: (LeaveStatus) -> Unit
) : ListAdapter<LeaveTypeItem, LeaveTypesAdapter.LeaveTypeViewHolder>(LeaveTypeDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LeaveTypeViewHolder {
        val binding = ItemLeaveTypeBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return LeaveTypeViewHolder(binding, onLeaveTypeSelected)
    }

    override fun onBindViewHolder(holder: LeaveTypeViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class LeaveTypeViewHolder(
        private val binding: ItemLeaveTypeBinding,
        private val onLeaveTypeSelected: (LeaveStatus) -> Unit
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: LeaveTypeItem) {
            binding.apply {
                // Set leave type info
                textViewLeaveIcon.text = item.icon
                textViewLeaveTitle.text = item.title
                
                // Set description
                if (item.description != null) {
                    textViewLeaveDescription.text = item.description
                    textViewLeaveDescription.visibility = android.view.View.VISIBLE
                } else {
                    textViewLeaveDescription.visibility = android.view.View.GONE
                }
                
                // Set selection state
                updateSelectionState(item.isSelected)
                
                // Set click listener
                root.setOnClickListener {
                    onLeaveTypeSelected(item.leaveStatus)
                }
            }
        }

        private fun updateSelectionState(isSelected: Boolean) {
            binding.apply {
                if (isSelected) {
                    imageViewSelectionIndicator.setImageResource(R.drawable.ic_radio_button_checked)
                    imageViewSelectionIndicator.visibility = android.view.View.VISIBLE
                    
                    // Highlight the card
                    root.strokeColor = ContextCompat.getColor(root.context, R.color.primary_color)
                    root.strokeWidth = 2
                    
                    // Update icon background
                    textViewLeaveIcon.background = ContextCompat.getDrawable(
                        root.context, 
                        R.drawable.bg_circle_icon_selected
                    )
                } else {
                    imageViewSelectionIndicator.setImageResource(R.drawable.ic_radio_button_unchecked)
                    imageViewSelectionIndicator.visibility = android.view.View.GONE
                    
                    // Remove highlight
                    root.strokeColor = ContextCompat.getColor(root.context, R.color.outline_variant)
                    root.strokeWidth = 1
                    
                    // Update icon background
                    textViewLeaveIcon.background = ContextCompat.getDrawable(
                        root.context, 
                        R.drawable.bg_circle_icon
                    )
                }
            }
        }
    }

    private class LeaveTypeDiffCallback : DiffUtil.ItemCallback<LeaveTypeItem>() {
        override fun areItemsTheSame(oldItem: LeaveTypeItem, newItem: LeaveTypeItem): Boolean {
            return oldItem.leaveStatus == newItem.leaveStatus
        }

        override fun areContentsTheSame(oldItem: LeaveTypeItem, newItem: LeaveTypeItem): Boolean {
            return oldItem == newItem
        }
    }
}
